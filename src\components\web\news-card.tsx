"use client";

import Image from "next/image";
import Link from "next/link";
import { Button } from "../ui/button";

interface Props {
  title: string;
  description: string;
  date: string;
  image: string;
}
function NewsCard({ title, description, date, image }: Props) {
  return (
    <div className="border border-primary rounded-lg shadow dark:border-secondary flex flex-col">
      <Image
        className="rounded-t-lg object-cover w-full h-48"
        src={image}
        alt={`Imagen de ${title}`}
        width={200}
        height={200}
        loading="lazy"
      />
      <div className="p-5">
        <h5 className="md:leading-3 mb-2 text-2xl font-bold tracking-tight text-gray-900 dark:text-white">
          {title}
        </h5>
        <span className="leading-none text-sm text-muted-foreground">
          Actualizado al{" "}
          {new Date(date).toLocaleDateString("es-ES", {
            year: "numeric",
            month: "long",
            day: "numeric",
          })}
        </span>
        <p className="mt-2 line-clamp-4 mb-3 font-normal text-gray-700 dark:text-secondary-foreground h-[100px]">
          {description}
        </p>
      </div>
      <Link href={"#"} className="self-end mb-2 mr-2">
        <Button type="button" variant={"secondary"}>
          Leer más
        </Button>
      </Link>
    </div>
  );
}

export default NewsCard;

"use client";
import * as React from "react";
import Link from "next/link";
import { Menu, X, ExternalLinkIcon, LucideBadgeCheck } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  Sheet<PERSON><PERSON>le,
  SheetTrigger,
  SheetClose,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { CMSService } from "@/services/cms";
import { ChevronDown } from "lucide-react";
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from "@/components/animate-ui/radix/collapsible";

export default function NavigationMobile() {
  const [isOpen, setIsOpen] = React.useState(false);
  const [openService, setOpenService] = React.useState<string | null>(null);
  const services = CMSService.getAllServices();

  const handleServiceToggle = (serviceId: string) => {
    setOpenService(openService === serviceId ? null : serviceId);
  };

  return (
    <div className="lg:hidden">
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button
            variant="ghost"
            size="sm"
            className="
              p-2 
              hover:bg-reiva-primary/10 
              transition-colors 
              duration-200
            "
          >
            <Menu
              className="h-6 w-6 text-reiva-primary-foreground"
              strokeWidth={2}
            />
            <span className="sr-only">Abrir menú</span>
          </Button>
        </SheetTrigger>

        <SheetContent
          side="right"
          className="
            w-full 
            sm:w-[400px] 
            bg-black/95 
            backdrop-blur-lg 
            border-l-reiva-primary/20
            p-0
          "
        >
          <SheetHeader className="px-6 py-4 border-b border-reiva-primary/20">
            <div className="flex items-center justify-between">
              <SheetTitle className="text-reiva-primary font-semibold text-lg">
                Menú
              </SheetTitle>
              <SheetClose asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="
                    p-2 
                    hover:bg-reiva-primary/10 
                    transition-colors 
                    duration-200
                  "
                >
                  <X className="h-5 w-5 text-reiva-primary-foreground" />
                </Button>
              </SheetClose>
            </div>
          </SheetHeader>

          <ScrollArea className="h-full px-6 py-4">
            <div className="space-y-6">
              {/* Servicios Section */}
              <div className="space-y-3">
                <h3 className="text-reiva-primary font-semibold text-base mb-4">
                  Servicios
                </h3>

                <div className="space-y-2">
                  {services.map((service) => (
                    <div key={service.id}>
                      <Collapsible
                        open={openService === service.id}
                        onOpenChange={() => handleServiceToggle(service.id)}
                      >
                        <CollapsibleTrigger asChild>
                          <Button
                            variant="ghost"
                            className="
                              w-full 
                              justify-between 
                              p-3 
                              h-auto 
                              text-left 
                              hover:bg-reiva-primary/10
                              transition-all 
                              duration-200
                              rounded-lg
                            "
                          >
                            <span className="text-reiva-primary-foreground font-medium">
                              {service.title}
                            </span>
                            <ChevronDown
                              className={cn(
                                "h-4 w-4 text-reiva-primary transition-transform duration-200",
                                openService === service.id && "rotate-180"
                              )}
                            />
                          </Button>
                        </CollapsibleTrigger>

                        <CollapsibleContent className="space-y-2 mt-2 ml-4">
                          <SheetClose asChild>
                            <Link
                              href={`/servicios/${service.slug}`}
                              className="
                                flex 
                                items-center 
                                gap-2 
                                p-2 
                                rounded-md 
                                hover:bg-reiva-primary/10 
                                transition-colors 
                                duration-200
                                group
                              "
                            >
                              <span className="text-sm font-medium text-reiva-primary">
                                Ver más detalles
                              </span>
                              <ExternalLinkIcon
                                size={14}
                                className="
                                  text-reiva-primary 
                                  group-hover:translate-x-1 
                                  transition-transform 
                                  duration-200
                                "
                              />
                            </Link>
                          </SheetClose>

                          <Separator className="bg-reiva-primary/20 my-2" />

                          <ul className="space-y-2">
                            {service.features.map((feat, index) => (
                              <li
                                key={index}
                                className="
                                  text-xs 
                                  flex 
                                  items-start 
                                  gap-2 
                                  text-gray-300
                                  pl-2
                                "
                              >
                                <LucideBadgeCheck
                                  size={14}
                                  className="
                                    text-reiva-primary 
                                    mt-0.5 
                                    flex-shrink-0
                                  "
                                />
                                <span>{feat}</span>
                              </li>
                            ))}
                          </ul>
                        </CollapsibleContent>
                      </Collapsible>
                    </div>
                  ))}
                </div>
              </div>

              <Separator className="bg-reiva-primary/20" />

              {/* Solicita asesoría */}
              <div className="space-y-3">
                <SheetClose asChild>
                  <Link
                    href="/#contacto"
                    className="
                      flex 
                      items-center 
                      gap-3 
                      p-4 
                      rounded-lg 
                      bg-reiva-primary/10 
                      hover:bg-reiva-primary/20 
                      transition-all 
                      duration-200 
                      border 
                      border-reiva-primary/30
                      group
                    "
                  >
                    <div
                      className="
                      w-10 
                      h-10 
                      rounded-full 
                      bg-reiva-primary 
                      flex 
                      items-center 
                      justify-center
                      group-hover:scale-110
                      transition-transform
                      duration-200
                    "
                    >
                      <ExternalLinkIcon size={18} className="text-black" />
                    </div>
                    <div>
                      <h4 className="text-reiva-primary font-semibold">
                        Solicita asesoría
                      </h4>
                      <p className="text-xs text-gray-300 mt-1">
                        Contáctanos para recibir atención personalizada
                      </p>
                    </div>
                  </Link>
                </SheetClose>
              </div>
            </div>
          </ScrollArea>
        </SheetContent>
      </Sheet>
    </div>
  );
}

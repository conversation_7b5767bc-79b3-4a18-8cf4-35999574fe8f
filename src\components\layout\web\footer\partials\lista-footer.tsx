import { FooterData } from "@/types/employee/footer-data-type";

export default function ListaFooter({
  footerData,
}: {
  footerData: FooterData;
}) {
  const { title, items } = footerData;
  return (
    <div>
      <h2 className="mb-6 text-sm font-semibold text-gray-900 uppercase dark:text-white shrink-0 text-nowrap">
        {title}
      </h2>
      <ul className="text-gray-500 dark:text-gray-300 font-medium text-sm">
        {items.map((item, index) => (
          <li key={index} className="mb-4 flex items-center gap-2">
            {item.link ? (
              <div className="hover:text-reiva-gris-suave flex items-center gap-2 cursor-pointer">
                {item.icon && (
                  <item.icon size={16} className="text-reiva-primary" />
                )}
                <a href={item.link} target="_blank" rel="noopener noreferrer">
                  {item.description}
                </a>
              </div>
            ) : (
              <>
                {item.icon && (
                  <item.icon size={16} className="text-reiva-primary" />
                )}
                <span>{item.description}</span>
              </>
            )}
          </li>
        ))}
      </ul>
    </div>
  );
}

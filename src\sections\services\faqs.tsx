"use client";

import {
  Accordion,
  AccordionButton,
  AccordionItem,
  AccordionPanel,
} from "@/components/animate-ui/headless/accordion";
import { ServiceFAQ } from "@/types/cms";
import { LucideHelpCircle } from "lucide-react";

export default function ServicesFAQs({ faqs }: { faqs: ServiceFAQ[] }) {
  return (
    <Accordion className="grid gap-8 max-w-3xl mx-auto">
      {faqs.map((faq, index) => (
        <AccordionItem key={index}>
          <AccordionButton className="flex gap-4">
            <LucideHelpCircle className="h-6 w-6 text-primary flex-shrink-0" />
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-white">
                {faq.question}
              </h3>
            </div>
          </AccordionButton>
          <AccordionPanel>{faq.answer}</AccordionPanel>
        </AccordionItem>
      ))}
    </Accordion>
  );
}

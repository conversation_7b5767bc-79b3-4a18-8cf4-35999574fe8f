import FormAsesoria from "@/components/web/asesoria/asesoria-form";
import Image from "next/image";

export default function ContactSection() {
  return (
    <div
      className="
            space-y-4 w-full mx-auto text-secondary
            flex
        "
    >
      <section className="w-full md:w-[60%] md:pr-12">
        <h1 className="title text-white">Solicita asesoría</h1>
        <p className="subtitle text-muted-foreground max-w-[1200px] font-semibold">
          Contamos con un equipo de profesionales dispuestos a ayudarte en lo
          que necesites, llena el siguiente formulario para ponerte en contacto
          con nosotros.
        </p>
        <div className="mt-4">
          <FormAsesoria />
        </div>
      </section>
      <section className="md:w-[40%] hidden md:block absolute -top-4 bottom-0 right-0">
        <Image
          src="/img/contact.webp"
          className="h-full w-full object-cover object-center"
          alt="Contacto"
          width={500}
          height={500}
          sizes="(max-width: 768px) 0vw, (max-width: 1200px) 50vw, 33vw"
          loading="lazy"
        />
      </section>
    </div>
  );
}

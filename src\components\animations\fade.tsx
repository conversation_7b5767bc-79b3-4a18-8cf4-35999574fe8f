"use client";
import React from "react";
import { HTMLMotionProps, motion } from "motion/react";

interface Props extends HTMLMotionProps<"div"> {
  children: React.ReactNode;
  delay?: number;
  direction?: "left" | "right" | "up" | "down";
  className?: string;
}

export function FadeUp({ children }: Props) {
  return (
    <motion.div
      initial="hidden"
      transition={{ duration: 0.5, delay: 0.3 }}
      viewport={{ once: true }}
      variants={{
        visible: { opacity: 1, y: 0 },
        hidden: { opacity: 0, y: 50 },
      }}
      whileInView={{ opacity: 1, y: 0 }}
    >
      {children}
    </motion.div>
  );
}

export function FadeRight({ children, delay }: Props) {
  return (
    <motion.div
      initial="hidden"
      transition={{ duration: 0.5, delay: delay || 0, ease: "easeIn" }}
      viewport={{ once: true, margin: "-50px" }}
      variants={{
        visible: { opacity: 1, x: 0 },
        hidden: { opacity: 0, x: -50 },
      }}
      whileInView={{ opacity: 1, x: 0 }}
    >
      {children}
    </motion.div>
  );
}

export function Fade({ children, delay, direction, ...props }: Props) {
  return (
    <motion.div
      {...props}
      initial="hidden"
      transition={{ duration: 0.5, delay: delay || 0, ease: "easeIn" }}
      viewport={{ once: true }}
      variants={{
        visible: { opacity: 1, x: 0, y: 0 },
        hidden: {
          opacity: 0,
          x:
            (direction === "left" || direction === "right") &&
            direction === "left"
              ? 50
              : -50,
          y:
            (direction === "up" || direction === "down") && direction === "up"
              ? -50
              : 50,
        },
      }}
      whileInView={{ opacity: 1, x: 0, y: 0 }}
    >
      {children}
    </motion.div>
  );
}

import { Fade, FadeUp } from "@/components/animations/fade";
import ServicesCarousel from "./hero/carousel";
import Servicios from "./services";
import ContactSection from "./contact";
import LocationSection from "./location";
import AnnouncementsSection from "./announcements";
import News from "./news";
import AboutUs from "../nosotros";

export default function Home() {
  return (
    <div className="flex flex-col gap-4">
      <AboutUs />

      <section className="space-y-8">
        <article className="w-full max-w-[2000px] mx-auto pb-12 md:pb-24">
          <Servicios />
        </article>

        <FadeUp>
          <article
            id="contacto"
            className="
              min-h-screen  md:min-h-[800px] max-h-[900px]
              flex justify-center
              relative
              overflow-hidden
              text-reiva-gris-suave
            "
          >
            <div className="container overflow-hidden rounded-md relative flex items-center">
              <ContactSection />
            </div>
          </article>
        </FadeUp>
      </section>

      <FadeUp>
        <LocationSection />
      </FadeUp>

      <ServicesCarousel />
      {/* Sección de cronogramas y anuncios */}
      {/* <AnnouncementsSection /> */}
    </div>
  );
}

"use client";
import {
  HoverCard,
  HoverCard<PERSON>ontent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import Image from "next/image";
import Logo from "@/assets/img/logo-reiva.svg";
import { ChevronDown } from "lucide-react";
import { motion } from "motion/react";
import Link from "next/link";
import ContactDetails from "./partials/contact-details";
import { useState } from "react";
import { useMediaQuery } from "@/hooks/web/use-media-query";

function NavInfo() {
  const isMobile = useMediaQuery("(max-width: 640px)");
  const [contactOpen, setContactOpen] = useState(false);
  function toggleContact() {
    console.log("toggleContact");
    if (!isMobile) return;
    setContactOpen(!contactOpen);
  }
  return (
    <nav className="flex justify-between items-center py-2 bg-transparent text-white">
      <div className="flex items-center justify-between gap-1 shrink-0 pl-2">
        <Link href="/">
          <Image
            alt="Logo de Reiva Estudio Contable"
            className="h-[70px] w-auto"
            src={Logo}
            width={0}
            height={0}
          />
        </Link>
      </div>
      <div className="flex space-x-2">
        <ul
          role="list"
          className="hidden sm:flex flex-wrap justify-end gap-1 md:gap-6 [&>li]:flex [&>li]:gap-2 [&>li]:items-center [&>li>span]:text-sm"
        >
          <ContactDetails />
        </ul>
        <HoverCard
          openDelay={0}
          closeDelay={50}
          open={isMobile ? contactOpen : undefined}
        >
          <div className="block sm:hidden text-sm">
            <HoverCardTrigger
              onClick={toggleContact}
              className="bg-reiva-primary px-2 py-1 rounded-md text-black cursor-pointer font-medium flex items-center"
            >
              <span>Contacto</span>
              <motion.div
                animate={{ rotate: contactOpen ? -180 : 0 }}
              >
                  <ChevronDown size={20} />
              </motion.div>
            </HoverCardTrigger>
            <HoverCardContent
              align="start"
              sideOffset={8}
              side="bottom"
              className="mr-8 *:list-none w-full"
            >
              <ContactDetails />
            </HoverCardContent>
          </div>
        </HoverCard>
      </div>
    </nav>
  );
}

export default NavInfo;

import z from "zod";

export const AsesoriaFormSchema = z
  .object({
    name: z.string().min(2, "El nombre debe tener al menos 2 caracteres"),
    business_size: z
      .enum(["micro", "pequeña", "mediana", "grande", "planeacion"])
      .optional(),
    service_type: z.string(),
    other_service: z.string().optional(),
    message: z.string().optional(),
  })
  .superRefine((val, ctx) => {
    // Validación de campo otro servicio

    if (val.service_type === "Otro" && !val.other_service) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "El campo otro servicio es requerido",
        path: ["other_service"],
      });
    }

    if (!val.service_type) {
      console.log(val.service_type);
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "El campo servicio es requerido",
        path: ["service_type"],
      });
    }
  });

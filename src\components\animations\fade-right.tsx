'use client';

import { motion } from 'framer-motion';

interface FadeRightProps {
  children: React.ReactNode;
  delay?: number;
}

export default function FadeRight({ children, delay = 0 }: FadeRightProps) {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      whileInView={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay }}
      viewport={{ once: true }}
    >
      {children}
    </motion.div>
  );
}
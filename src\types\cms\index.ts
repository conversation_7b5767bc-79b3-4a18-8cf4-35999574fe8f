export interface CMSContent {
  id: string;
  title: string;
  description: string;
  content: string;
  image?: string;
  createdAt: string;
  updatedAt: string;
  slug: string;
  metadata?: {
    keywords: string[];
    author?: string;
  };
}

export type Benefit = {
  title: string;
  description: string;
};
export type ServiceFAQ = {
  question: string;
  answer: string;
};
export interface ServiceContent extends CMSContent {
  category: ServiceCategory;
  features: string[];
  benefits: Benefit[];
  pricing?: {
    startingFrom: number;
    currency: string;
    billingPeriod?: string;
  };
  faqs?: ServiceFAQ[];
}

export interface AboutUsContent extends CMSContent {
  teamMembers: {
    id: string;
    name: string;
    position: string;
    bio: string;
    image?: string;
    socialLinks?: {
      platform: string;
      url: string;
    }[];
  }[];
  history: {
    year: number;
    milestone: string;
  }[];
  values: {
    title: string;
    description: string;
    icon?: string;
  }[];
  certifications?: {
    name: string;
    issuer: string;
    year: number;
    image?: string;
  }[];
}

export interface AnnouncementContent {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  details: string[];
  deadline: string;
  contact: string;
  location: string;
  image?: string;
  type: "promotional" | "deadline" | "news";
  priority: "high" | "medium" | "low";
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export enum ServiceCategory {
  ASESORIA_CONTABLE = "Asesoría Contable",
  ASESORIA_TRIBUTARIA = "Asesoría Tributaria",
  CONTRATACIONES_PUBLICAS = "Contrataciones Públicas",
}

import { NextResponse } from "next/server"

export async function POST(request : Request) {
    const body = await request.json()

    if(!body.captcha){
        // return new Response('Captcha is required', { status: 400 })
        return NextResponse.json("Captcha is required", {status: 400})
    }

    try{
        const captcha_secret = process.env.RECAPTCHA_SECRET_KEY
        const captcha = body.captcha
        const response = await fetch(`https://www.google.com/recaptcha/api/siteverify?secret=${captcha_secret}&response=${captcha}`, {
        method: 'POST',  
        headers: {
            "Content-Type": "application/x-www-form-urlencoded; charset=utf-8"
          }
        })

        const captchaValidation = await response.json()
        if (captchaValidation.success){
            return NextResponse.json("Captcha validated", {status: 200})
        }

        return NextResponse.json("Invalid captcha", {status: 400})
    }catch(e){
        console.error(e)
        return NextResponse.json("Captcha is required", {status: 500})
    }
}
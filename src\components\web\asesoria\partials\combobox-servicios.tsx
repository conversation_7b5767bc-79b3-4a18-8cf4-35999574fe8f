"use client";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { cn } from "@/lib/utils";
import { AsesoriaFormSchema } from "../schema";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { set, z } from "zod";
import { SERVICES } from "@/constants/web/services";
import { CaretSortIcon, CheckIcon } from "@radix-ui/react-icons";
import { ServiceCategory } from "@/types/services";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";

export default function ComboboxServicios({
  form,
}: {
  form: ReturnType<typeof useForm<z.infer<typeof AsesoriaFormSchema>>>;
}) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <FormField
      control={form.control}
      name="service_type"
      render={({ field }) => (
        <FormItem className="flex flex-col">
          <FormLabel className="flex items-center justify-between">
            <span>Busco asesoría sobre</span>
            <Button type="button" className="text-white px-4 text-xs py-1 h-fit" variant={"outline"} onClick={() => form.setValue("service_type", "Otro")}>
              Otro servicio
            </Button>
          </FormLabel>
          <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
              <FormControl>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isOpen}
                  className={cn(
                    "w-full justify-between text-reiva-gris-suave overflow-clip",
                    !field.value && "text-muted-foreground"
                  )}
                >
                  {field.value
                    ? SERVICES.find((service) => service.name === field.value)
                        ?.name ?? "Otro"
                    : "Seleccione un servicio"}
                  <CaretSortIcon className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </FormControl>
            </PopoverTrigger>
            <PopoverContent
              align="start"
              side="top"
              className="w-[85vw] md:w-[50vw] px-2"
            >
              <Command>
                <CommandInput
                  placeholder="Buscar un servicio"
                  className="h-9"
                />
                <CommandEmpty>
                  <Button 
                    type="button"
                    onClick={() => form.setValue("service_type", "Otro")}>
                    Otro
                  </Button>
                </CommandEmpty>
                <CommandGroup
                  className="h-[200px] overflow-y-auto"
                  style={{ scrollbarWidth: "thin" }}
                >
                  {Object.keys(ServiceCategory).map((category) => {
                    return (
                      <div key={category}>
                        <h2 className="text-reiva-primary text-md px-2 rounded-md mb-1">
                          {
                            ServiceCategory[
                              category as keyof typeof ServiceCategory
                            ]
                          }
                        </h2>
                        <Separator />
                        {SERVICES.filter(
                          (service) =>
                            service.category ===
                            ServiceCategory[
                              category as keyof typeof ServiceCategory
                            ]
                        ).map((service) => (
                          <CommandItem
                            value={service.name}
                            key={service.id}
                            onSelect={() => {
                              form.setValue("other_service", "");
                              form.setValue("service_type", service.name);
                              setIsOpen(false);
                            }}
                          >
                            {service.name}
                            <CheckIcon
                              className={cn(
                                "ml-auto h-4 w-4",
                                service.name === field.value
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                          </CommandItem>
                        ))}
                      </div>
                    );
                  })}
                </CommandGroup>
              </Command>
            </PopoverContent>
          </Popover>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

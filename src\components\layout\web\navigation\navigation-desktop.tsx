"use client";
import * as React from "react";
import Link from "next/link";
import Image from "next/image";
import { ExternalLinkIcon, LucideBadgeCheck } from "lucide-react";
import { cn } from "@/lib/utils";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import { Separator } from "@/components/ui/separator";
import { CMSService } from "@/services/cms";
import NavigationMobile from "./navigation-mobile";

export default function NavBar() {
  const services = CMSService.getAllServices();

  return (
    <div className="flex justify-between items-center p-2">
      {/* Desktop Navigation */}
      <NavigationMenu className="hidden lg:flex">
        <NavigationMenuList>
          <NavigationMenuItem>
            <NavigationMenuTrigger>Servicios</NavigationMenuTrigger>
            <NavigationMenuContent>
              <ul
                className="
                  grid sm:grid-cols-2 lg:grid-cols-3 
                  gap-1 p-2 py-5 
                  w-[95vw] max-w-[1350px]
                  overflow-y-auto"
                style={{
                  scrollbarWidth: "thin",
                  scrollbarGutter: "stable",
                }}
              >
                {services.map((service) => (
                  <div role="gridcell" key={service.id} className="text-sm">
                    <LinkItem
                      href={`/servicios/${service.slug}`}
                      title={service.title}
                    />
                    <Separator className="mb-2" />
                    <ul className="px-3 space-y-2">
                      {service.features.map((feat, index) => {
                        return (
                          <li
                            key={index}
                            className="text-sm flex items-center gap-1.5"
                          >
                            <LucideBadgeCheck
                              size={15}
                              className="rounded-sm"
                            />
                            <span>{feat}</span>
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                ))}
              </ul>
            </NavigationMenuContent>
          </NavigationMenuItem>

          <NavigationMenuItem>
            <Link href="/#contacto" legacyBehavior passHref>
              <NavigationMenuLink className={navigationMenuTriggerStyle()}>
                Solicita asesoría
              </NavigationMenuLink>
            </Link>
          </NavigationMenuItem>
        </NavigationMenuList>
      </NavigationMenu>

      {/* Mobile Navigation */}
      <NavigationMobile />
    </div>
  );
}

export const LinkItem = ({
  className,
  title,
  href,
}: {
  className?: string;
  title: string;
  href: string;
}) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <Link
          href={href}
          className={cn(
            "flex flex-row items-center select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent focus:bg-accent focus:text-accent-foreground",
            className
          )}
        >
          <div className="text-sm font-medium leading-none text-reiva-primary-foreground dark:text-reiva-primary">
            {title}
          </div>
          <ExternalLinkIcon size={16} className="inline-block ml-auto" />
        </Link>
      </NavigationMenuLink>
    </li>
  );
};

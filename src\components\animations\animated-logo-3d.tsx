import React, { useRef, useEffect, useState } from "react";
import * as THREE from "three";
import Image from "next/image";

const AnimatedLogo3D = () => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const cameraRef = useRef<THREE.PerspectiveCamera | null>(null);
  const logoMeshRef = useRef<THREE.Group | null>(null);
  const mouseRef = useRef({ x: 0, y: 0 });
  const isMouseDownRef = useRef(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Use the actual Reiva logo
  const logoImageData = "/img/reiva.png";

  useEffect(() => {
    if (!mountRef.current) return;

    // Copy refs for cleanup
    const currentMount = mountRef.current;

    // Scene setup
    const scene = new THREE.Scene();
    scene.background = null; // Transparent background
    sceneRef.current = scene;

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      currentMount.clientWidth / currentMount.clientHeight,
      0.1,
      1000
    );
    camera.position.set(0, 0, 7); // Moved closer for larger appearance
    cameraRef.current = camera;

    // Renderer setup with shadow mapping and color space
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(currentMount.clientWidth, currentMount.clientHeight);
    renderer.setClearColor(0x000000, 0); // Transparent clear color
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.outputColorSpace = THREE.SRGBColorSpace; // Better color rendering
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.2; // Slightly brighter exposure
    currentMount.appendChild(renderer.domElement);
    rendererRef.current = renderer;

    // Optimized lighting setup for vibrant golden colors
    const ambientLight = new THREE.AmbientLight(0xffa500, 0.4); // Golden ambient
    scene.add(ambientLight);

    const directionalLight = new THREE.DirectionalLight(0xffffff, 1.2);
    directionalLight.position.set(5, 5, 5);
    directionalLight.castShadow = true;
    scene.add(directionalLight);

    // Golden accent lights to enhance the logo colors
    const pointLight1 = new THREE.PointLight(0xffd700, 1.0, 100);
    pointLight1.position.set(-5, 5, 5);
    scene.add(pointLight1);

    const pointLight2 = new THREE.PointLight(0xff8c00, 0.8, 100);
    pointLight2.position.set(5, -5, 5);
    scene.add(pointLight2);

    const pointLight3 = new THREE.PointLight(0xffb347, 0.6, 100);
    pointLight3.position.set(0, 0, 10);
    scene.add(pointLight3);

    // Load texture from the logo image with enhanced settings
    const textureLoader = new THREE.TextureLoader();
    const logoTexture = textureLoader.load(logoImageData, () => {
      setIsLoaded(true);
    });
    logoTexture.generateMipmaps = false;
    logoTexture.minFilter = THREE.LinearFilter;
    logoTexture.magFilter = THREE.LinearFilter;

    // Create only the logo plane
    const logoGroup = new THREE.Group();
    scene.add(logoGroup);

    // Main logo plane with enhanced material for vibrant colors - increased size for better visibility
    const planeGeometry = new THREE.PlaneGeometry(5.5, 5.5);
    const planeMaterial = new THREE.MeshStandardMaterial({
      map: logoTexture,
      transparent: true,
      alphaTest: 0.1,
      side: THREE.DoubleSide,
      metalness: 0.1,
      roughness: 0.3,
      emissive: 0x221100, // Slight golden emissive glow
      emissiveIntensity: 0.1,
    });
    const logoPlane = new THREE.Mesh(planeGeometry, planeMaterial);
    logoPlane.castShadow = true;
    logoGroup.add(logoPlane);

    logoMeshRef.current = logoGroup;

    // Mouse event handlers
    const handleMouseMove = (event: MouseEvent) => {
      if (!mountRef.current) return;
      const rect = mountRef.current.getBoundingClientRect();
      mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    };

    const handleMouseDown = () => {
      isMouseDownRef.current = true;
    };

    const handleMouseUp = () => {
      isMouseDownRef.current = false;
    };

    const handleClick = () => {
      // Trigger explosive animation
      logoGroup.scale.set(1.2, 1.2, 1.2);
      setTimeout(() => {
        logoGroup.scale.set(1, 1, 1);
      }, 200);
    };

    // Add event listeners
    currentMount.addEventListener("mousemove", handleMouseMove);
    currentMount.addEventListener("mousedown", handleMouseDown);
    currentMount.addEventListener("mouseup", handleMouseUp);
    currentMount.addEventListener("click", handleClick);

    // Animation loop
    const animate = () => {
      requestAnimationFrame(animate);

      const time = Date.now() * 0.001;

      // Logo rotation based on mouse interaction
      if (isMouseDownRef.current) {
        const targetRotationY = mouseRef.current.x * Math.PI;
        const targetRotationX = -mouseRef.current.y * Math.PI * 0.5;
        logoGroup.rotation.y += (targetRotationY - logoGroup.rotation.y) * 0.1;
        logoGroup.rotation.x += (targetRotationX - logoGroup.rotation.x) * 0.1;
      } else {
        // Auto rotation
        logoGroup.rotation.y += 0.005;
        logoGroup.rotation.x = Math.sin(time * 0.5) * 0.1;
      }

      // Floating animation
      logoGroup.position.y = Math.sin(time) * 0.2;
      logoGroup.position.z = Math.sin(time * 0.7) * 0.1;

      // Breathing effect (more subtle)
      const scale = 1 + Math.sin(time * 2) * 0.02;
      logoPlane.scale.set(scale, scale, 1);

      renderer.render(scene, camera);
    };

    animate();

    // Handle resize
    const handleResize = () => {
      if (!mountRef.current || !cameraRef.current || !rendererRef.current)
        return;
      const width = mountRef.current.clientWidth;
      const height = mountRef.current.clientHeight;

      cameraRef.current.aspect = width / height;
      cameraRef.current.updateProjectionMatrix();
      rendererRef.current.setSize(width, height);
    };

    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => {
      const currentRenderer = rendererRef.current;

      if (currentMount) {
        currentMount.removeEventListener("mousemove", handleMouseMove);
        currentMount.removeEventListener("mousedown", handleMouseDown);
        currentMount.removeEventListener("mouseup", handleMouseUp);
        currentMount.removeEventListener("click", handleClick);

        if (
          currentRenderer &&
          currentMount.contains(currentRenderer.domElement)
        ) {
          currentMount.removeChild(currentRenderer.domElement);
        }
      }
      window.removeEventListener("resize", handleResize);

      if (currentRenderer) {
        currentRenderer.dispose();
      }
    };
  }, []);

  return (
    <div className="w-full h-fit relative overflow-hidden aspect-square max-h-[250px] md:max-h-[400px] lg:max-h-[500px] ">
      <div
        ref={mountRef}
        className="w-full h-full cursor-grab active:cursor-grabbing"
      />

      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Image
            src="/img/reiva.png"
            alt="Reiva Logo"
            width={200}
            height={200}
            className="object-contain animate-pulse w-[150px] h-[150px] md:w-[250px] md:h-[250px] lg:w-[300px] lg:h-[300px]"
          />
        </div>
      )}
    </div>
  );
};

export default AnimatedLogo3D;

import { getToken } from 'next-auth/jwt'
import { NextRequest, NextResponse } from 'next/server'

export async function middleware (req: NextRequest) {
    const secret = process.env.NEXTAUTH_SECRET
    const session = await getToken({ req, secret: secret })
    console.log('Current session Middleware', session)
    if (!session) {
      const requestedPage = req.nextUrl.pathname
      const url = req.nextUrl.clone()
      url.pathname = '/login'
      url.search = `?p=${requestedPage}`
      return NextResponse.redirect(url)
    }
    return NextResponse.next()
  }

export const config = {
  matcher: ['/app/:path*'],
  }
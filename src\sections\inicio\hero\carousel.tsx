"use client";
import { useEffect, useState } from "react";
import Autoplay from "embla-carousel-autoplay";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";
import CarouselSlide from "./carousel-item";
import { cn } from "@/lib/utils";
import { HERO_CAROUSEL_ITEMS } from "./constants";

const CAROUSEL_DELAY = 5000;

export default function ServicesCarousel() {
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [count, setCount] = useState(0);
  const [play, setPlay] = useState(true);

  useEffect(() => {
    if (!api) {
      return;
    }

    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);

  function handleScrollClick(index: number) {
    api?.scrollTo(index);
    setPlay(false);
  }

  return (
    <div className="w-full max-w-[2000px] mx-auto md:h-full min-h-[400px] md:min-h-[500px] lg:min-h-[600px] max-h-[710px] md:-mt-[58px]">
      <Carousel
        setApi={setApi}
        className="relative w-full overflow-hidden h-full"
        plugins={[
          Autoplay({
            active: play,
            delay: CAROUSEL_DELAY,
            stopOnFocusIn: true,
          }),
        ]}
        opts={{
          loop: true,
        }}
        onMouseLeave={() => setPlay(true)}
      >
        <CarouselContent className="h-full">
          {HERO_CAROUSEL_ITEMS.map(({ title, description, image }, index) => (
            <CarouselItem
              key={index}
              className="h-[500px] lg:h-[700px] lg:max-h-[700px]"
            >
              <CarouselSlide
                title={title}
                description={description}
                image={image}
              />
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>

      {/* indicadores */}
      <div className="py-3 md:py-4 flex justify-center gap-2">
        {Array.from({ length: count }).map((_, index) => (
          <span
            key={index}
            className={cn(
              "rounded-full w-3 h-3 md:w-2.5 md:h-2.5 p-0 cursor-pointer transition-all duration-300 ease-in-out hover:scale-110",
              current === index + 1
                ? "bg-reiva-primary w-10 md:w-8"
                : "bg-muted-foreground dark:bg-muted-foreground hover:bg-reiva-primary/70"
            )}
            onClick={() => handleScrollClick(index)}
          ></span>
        ))}
      </div>
    </div>
  );
}

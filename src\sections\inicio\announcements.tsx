"use client";
import { CMSService } from "@/services/cms";
import { FadeUp } from "@/components/animations/fade";
import { Calendar, Phone, ArrowRight, Play } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

export default function AnnouncementsSection() {
  const announcements = CMSService.getFeaturedAnnouncements(4);

  return (
    <section className="py-16 md:py-24 bg-black">
      <div className="container mx-auto px-4">
        <FadeUp>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-reiva-gris-suave mb-4">
              <span className="text-reiva-primary">Cronogramas</span> y Anuncios
            </h2>
            <p className="text-reiva-gris-suave text-lg max-w-2xl mx-auto">
              Mantente al día con los vencimientos tributarios y anuncios
              importantes
            </p>
          </div>
        </FadeUp>

        <FadeUp delay={0.2}>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6 max-w-6xl mx-auto">
            {announcements.map((announcement, index) => (
              <article
                key={announcement.id}
                className={cn(
                  "relative overflow-hidden rounded-2xl bg-black/80 backdrop-blur-sm shadow-2xl",
                  index === 0 && "md:col-span-2 lg:col-span-1",
                  index === 3 &&
                    announcements.length === 4 &&
                    "md:col-span-2 lg:col-span-1"
                )}
              >
                {/* Contenido principal */}
                <div className="relative z-10 p-6 md:p-8 text-reiva-gris-suave">
                  {/* Header con logo */}
                  <header className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 bg-reiva-primary-foreground rounded-lg flex items-center justify-center">
                      <span className="text-lg font-bold text-black">R</span>
                    </div>
                    <div>
                      <p className="text-reiva-primary-foreground font-semibold text-xs tracking-wide">
                        REIVA ESTUDIO CONTABLE
                      </p>
                      <h3 className="text-lg md:text-xl font-bold text-reiva-gris-suave">
                        {announcement.title}
                      </h3>
                    </div>
                  </header>

                  {/* Subtítulo */}
                  <h4 className="text-base md:text-lg font-bold mb-4 text-reiva-primary-foreground">
                    {announcement.subtitle}
                  </h4>

                  {/* Descripción */}
                  <div className="space-y-2 mb-6">
                    <p className="font-semibold text-reiva-gris-suave">
                      {announcement.description}
                    </p>
                    {announcement.details
                      .slice(0, 2)
                      .map((detail, detailIndex) => (
                        <p
                          key={detailIndex}
                          className="text-sm text-reiva-gris-suave/80 leading-relaxed"
                        >
                          • {detail}
                        </p>
                      ))}
                    {announcement.details.length > 2 && (
                      <p className="text-sm text-reiva-primary-foreground">
                        +{announcement.details.length - 2} más...
                      </p>
                    )}
                  </div>

                  {/* Fecha límite */}
                  <div className="bg-reiva-primary-foreground/10 backdrop-blur-sm rounded-lg p-4 mb-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="w-4 h-4 text-reiva-primary-foreground" />
                      <span className="font-semibold text-reiva-gris-suave text-sm">
                        {announcement.type === "promotional"
                          ? "Información:"
                          : "Fecha límite:"}
                      </span>
                    </div>
                    <p className="text-sm font-bold text-reiva-primary-foreground">
                      {announcement.deadline}
                    </p>
                  </div>

                  {/* Contacto */}
                  <footer className="flex items-center justify-between pt-4 border-t border-reiva-gris-suave/20">
                    <div className="flex items-center gap-2">
                      <Phone className="w-3 h-3 text-reiva-primary-foreground" />
                      <span className="font-semibold text-xs text-reiva-gris-suave">
                        {announcement.contact}
                      </span>
                    </div>
                    <span className="text-xs text-reiva-gris-suave/75">
                      {announcement.location}
                    </span>
                  </footer>

                  {/* Indicador de tipo */}
                  {announcement.type === "promotional" && (
                    <div className="absolute top-4 right-4">
                      <Play className="w-5 h-5 text-reiva-primary-foreground" />
                    </div>
                  )}
                </div>
              </article>
            ))}
          </div>

          {/* Botón Ver Más */}
          {announcements.length >= 4 && (
            <div className="text-center mt-8">
              <Button
                variant="outline"
                className="bg-transparent border-reiva-primary-foreground text-reiva-primary-foreground hover:bg-reiva-primary-foreground hover:text-black"
              >
                Ver más anuncios
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )}
        </FadeUp>
      </div>
    </section>
  );
}

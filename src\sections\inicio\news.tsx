"use client";
import { LucideArrowRightCircle } from "lucide-react";
import NewsCard from "@/components/web/news-card";
import Link from "next/link";

const NEWS = [
  {
    title: "Títu<PERSON> de la noticia",
    description: "loremp ipsum dolor sit amet, consectetur adipiscing elit. <PERSON>ullam delectus, odio etiam. consectetur adipiscing elit. <PERSON>ullam delectus, odio etiam. consectetur adipiscing elit. Nullam delectus, odio etiam.",
    date: "2022-01-01T00:00:00Z",
    image: "/img/asesoria_tributaria.webp",
  },
  {
    title: "Título de la noticia",
    description: "consectetur adipiscing elit. <PERSON><PERSON>am delectus, odio etiam. consectetur adipiscing elit. Nullam delectus, odio etiam. ",
    date: "2022-01-01T00:00:00Z",
    image: "/img/asesoria_tributaria.webp",
  },
  {
    title: "T<PERSON><PERSON><PERSON> de la noticia",
    description: "consectetur adipiscing elit. <PERSON><PERSON><PERSON> delectus, odio etiam.consectetur adipiscing elit. <PERSON><PERSON><PERSON> delectus, odio etiam.",
    date: "2022-01-01T00:00:00Z",
    image: "/img/asesoria_tributaria.webp",
  },
  {
    title: "Título de la noticia",
    description: "consectetur adipiscing elit. Nullam delectus, odio etiam. consectetur adipiscing elit. Nullam delectus, odio etiam. consectetur adipiscing elit. Nullam delectus, odio etiam. consectetur adipiscing elit. Nullam delectus, odio etiam.",
    date: "2022-01-01T00:00:00Z",
    image: "/img/asesoria_tributaria.webp",
  },
];

function News() {
  return (
    <div className="flex gap-x-12 snap-x overflow-x-auto overflow-hidden p-0 md:p-8"
      style={{ scrollbarWidth: "none" }}
    >
      {NEWS.map((news, index) => (
        <div key={index} className="snap-start w-full md:w-1/2 lg:w-1/3 shrink-0">
          <NewsCard {...news} />
        </div>
      ))}
      <Link href="#" className="flex items-center gap-2 text-nowrap my-auto snap-end animate-pulse shrink-0">
        Ver más
        <LucideArrowRightCircle size={30}/>
      </Link>
    </div>
  );
}

export default News;

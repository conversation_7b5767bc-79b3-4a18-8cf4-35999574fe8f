import type { Metadata } from "next";

import WebFooter from "@/components/layout/web/footer/footer";
import Header from "@/components/layout/web/navigation/header";



import WhatsAppFloating from "@/components/layout/web/floating-whatsapp";

export const metadata: Metadata = {
  title: "Inicio | Reiva Estudio Contable",
  description: "Expertos en servicios contables, tributarios y asesoría empresarial. Soluciones profesionales adaptadas a las necesidades de tu empresa en Perú.",
  openGraph: {
    title: "Reiva Estudio Contable - Servicios Contables Profesionales",
    description: "Expertos en servicios contables, tributarios y asesoría empresarial. Soluciones profesionales adaptadas a las necesidades de tu empresa en Perú.",
    images: ['/img/og-image.jpg']
  }
};

function HomeLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="relative">
      <Header />

      <main className="mb-8">{children}</main>

      <WebFooter />
      {/* WhatsApp */}
      <WhatsAppFloating />
    </div>
  );
}

export default HomeLayout;

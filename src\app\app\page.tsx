"use client"
import { But<PERSON> } from '@/components/ui/button';
import { useSession } from 'next-auth/react'
import { signOut } from 'next-auth/react';

function Home() {
    const { data: session } = useSession()
    return (
        <div>
            Sistema de administración de noticias y novedades {session?.user?.email}
            {/* <Button onClick={() => }>Click me</Button> */}
            <Button onClick={() => signOut()}>Sign out</Button>
        </div>
    );
}

export default Home;
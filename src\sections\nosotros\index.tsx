"use client";

import { useEffect, useState } from "react";
import { AboutUsContent } from "@/types/cms";
import { CMSService } from "@/services/cms";
import { FadeUp } from "@/components/animations/fade";
import { Calendar, Award, Users, Trophy, BadgeCheck } from "lucide-react";
import Image from "next/image";
import AnimatedLogo3D from "@/components/animations/animated-logo-3d";

export default function AboutUs() {
  const aboutData = CMSService.getAboutUsContent();

  if (!aboutData) return null;

  return (
    <div className="">
      {/* Hero Section */}
      <section className="relative py-20 md:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-reiva-primary/5 via-transparent to-black"></div>
        <div className="container relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            {/* Contenido de texto */}
            <FadeUp>
              <div className="space-y-8 order-2 lg:order-1">
                <div className="space-y-4">
                  <div className="inline-flex items-center px-4 py-2 bg-reiva-primary/10 rounded-full">
                    <span className="text-reiva-primary font-semibold text-sm">
                      Profesionalismo y Confianza
                    </span>
                  </div>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight">
                    <span className="text-reiva-primary">REIVA</span>{" "}
                    <span className="text-white">ESTUDIO CONTABLE</span>
                  </h1>
                  <p className="text-xl text-reiva-gris-suave leading-relaxed">
                    {aboutData.description}
                  </p>
                </div>
                <div className="prose prose-lg max-w-none">
                  <p className="text-reiva-gris-suave leading-relaxed">
                    {aboutData.content}
                  </p>
                </div>
                {/* <div className="flex flex-wrap gap-4">
                  <div className="flex items-center gap-2 px-4 py-2 bg-black/20 backdrop-blur-sm rounded-lg shadow-sm border border-reiva-primary-foreground/20">
                    <BadgeCheck className="w-5 h-5 text-reiva-primary-foreground" />
                    <span className="text-sm font-medium text-reiva-gris-suave">
                      Profesionales Certificados
                    </span>
                  </div>
                </div> */}
              </div>
            </FadeUp>

            {/* Logo animado */}
            <FadeUp delay={0.2}>
              <div className="relative order-1 lg:order-2 flex justify-center lg:justify-end">
                <div className="relative rounded-2xl overflow-hidden shadow-2xl w-full md:max-w-[400px] h-[200px] md:h-full">
                  <AnimatedLogo3D />
                </div>
              </div>
            </FadeUp>
          </div>
        </div>
      </section>

      {/* Valores */}
      <section className="py-20 bg-black/80 backdrop-blur-sm -mt-32">
        <div className="container">
          <FadeUp>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4 text-reiva-gris-suave">
                Nuestros{" "}
                <span className="text-reiva-primary-foreground">Valores</span>
              </h2>
              <p className="text-reiva-gris-suave text-lg max-w-2xl mx-auto">
                Los principios que guían nuestro trabajo y definen nuestra
                excelencia profesional
              </p>
            </div>
          </FadeUp>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {aboutData.values.map((value, index) => {
              const icons = [Trophy, BadgeCheck, Users, Award];
              const IconComponent = icons[index % icons.length];

              return (
                <FadeUp key={value.title} delay={index * 0.1}>
                  <div className="group relative p-8 bg-black/40 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-reiva-primary-foreground/20 hover:border-reiva-primary-foreground/50">
                    <div className="absolute inset-0 bg-gradient-to-br from-reiva-primary-foreground/10 to-reiva-primary-foreground/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <div className="relative z-10 space-y-4">
                      <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-reiva-primary-foreground to-reiva-primary flex items-center justify-center shadow-lg">
                        <IconComponent className="w-8 h-8 text-black" />
                      </div>
                      <h3 className="text-xl font-bold text-reiva-gris-suave group-hover:text-reiva-primary-foreground transition-colors">
                        {value.title}
                      </h3>
                      <p className="text-reiva-gris-suave leading-relaxed">
                        {value.description}
                      </p>
                    </div>
                  </div>
                </FadeUp>
              );
            })}
          </div>
        </div>
      </section>
    </div>
  );
}

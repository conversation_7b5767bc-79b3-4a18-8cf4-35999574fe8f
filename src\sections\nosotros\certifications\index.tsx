import { FadeUp } from "@/components/animations/fade";
import { CMSService } from "@/services/cms";
import { Award, Calendar } from "lucide-react";
import Image from "next/image";

export default function index() {
  const aboutData = CMSService.getAboutUsContent();

  return (
    aboutData.certifications &&
    aboutData.certifications.length > 0 && (
      <section className="py-20 bg-reiva-primary">
        <div className="container">
          <FadeUp>
            <div className="text-center mb-16">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4 text-reiva-gris-suave">
                <span className="text-reiva-primary-foreground">
                  Certificaciones
                </span>{" "}
                y Reconocimientos
              </h2>
              <p className="text-reiva-gris-suave text-lg max-w-2xl mx-auto">
                Nuestro compromiso con la calidad respaldado por certificaciones
                internacionales
              </p>
            </div>
          </FadeUp>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {aboutData.certifications.map((cert, index) => (
              <FadeUp key={index} delay={index * 0.1}>
                <div className="group relative p-8 bg-black/40 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 border border-reiva-primary-foreground/20 hover:border-reiva-primary-foreground/50">
                  <div className="absolute inset-0 bg-gradient-to-br from-reiva-primary-foreground/10 to-reiva-primary-foreground/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative z-10 flex items-center gap-6">
                    <div className="relative h-20 w-20 flex-shrink-0 p-2 bg-gradient-to-br from-reiva-primary-foreground/20 to-reiva-primary/20 rounded-xl">
                      <Image
                        src={cert.image || ""}
                        alt={cert.name}
                        fill
                        className="object-contain p-2"
                      />
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-xl font-bold text-reiva-gris-suave group-hover:text-reiva-primary-foreground transition-colors">
                        {cert.name}
                      </h3>
                      <p className="text-reiva-gris-suave font-medium">
                        {cert.issuer}
                      </p>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-reiva-primary-foreground" />
                        <p className="text-sm text-reiva-gris-suave">
                          Año: {cert.year}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="absolute top-4 right-4 w-8 h-8 bg-reiva-primary/10 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Award className="w-4 h-4 text-reiva-primary" />
                  </div>
                </div>
              </FadeUp>
            ))}
          </div>
        </div>
      </section>
    )
  );
}

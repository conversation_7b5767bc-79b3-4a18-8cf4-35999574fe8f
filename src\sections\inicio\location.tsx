"use client";
import Image from "next/image";
import { MapPin, Clock, Phone, Mail } from "lucide-react";
import { FadeUp } from "@/components/animations/fade";
import { CMSService } from "@/services/cms";

export default function LocationSection() {
  const locationData = CMSService.getLocationContent();

  return (
    <section className="py-16 md:py-24 bg-black/80 backdrop-blur-sm">
      <div className="container mx-auto px-4">
        <FadeUp>
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-reiva-gris-suave mb-4">
              <span className="text-reiva-primary-foreground">
                {locationData.title}
              </span>
            </h2>
            <p className="text-reiva-gris-suave text-lg max-w-2xl mx-auto">
              {locationData.description}
            </p>
          </div>
        </FadeUp>

        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12">
          {/* Información de contacto y imagen del local */}
          <FadeUp delay={0.2}>
            <div className="space-y-8">
              {/* Imagen del local */}
              <div className="relative overflow-hidden rounded-2xl shadow-2xl">
                <Image
                  src={locationData.images.main}
                  alt="Entrada del local - Reiva Estudio Contable"
                  width={600}
                  height={400}
                  className="w-full h-64 md:h-96 object-cover transition-transform duration-300 hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-reiva-gris-suave">
                  <p className="text-sm font-medium">Nuestras oficinas</p>
                  <p className="text-xs opacity-90">
                    {locationData.address.street}
                  </p>
                </div>
              </div>

              {/* Información de contacto */}
              <div className="bg-black/60 backdrop-blur-sm rounded-2xl p-6 ">
                <h3 className="text-xl font-semibold text-reiva-gris-suave mb-6">
                  Información de Contacto
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-reiva-primary-foreground/20 rounded-lg flex items-center justify-center">
                      <Phone className="w-5 h-5 text-reiva-primary-foreground" />
                    </div>
                    <div>
                      <p className="text-reiva-gris-suave font-medium">
                        Teléfono
                      </p>
                      <p className="text-reiva-gris-suave text-sm">
                        {locationData.contact.phone}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-reiva-primary-foreground/20 rounded-lg flex items-center justify-center">
                      <Mail className="w-5 h-5 text-reiva-primary-foreground" />
                    </div>
                    <div>
                      <p className="text-reiva-gris-suave font-medium">Email</p>
                      <p className="text-reiva-gris-suave text-sm">
                        {locationData.contact.email}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 bg-reiva-primary-foreground/20 rounded-lg flex items-center justify-center">
                      <Clock className="w-5 h-5 text-reiva-primary-foreground" />
                    </div>
                    <div>
                      <p className="text-reiva-gris-suave font-medium">
                        Horarios de Atención
                      </p>
                      <p className="text-reiva-gris-suave text-sm">
                        {locationData.hours.weekdays}
                      </p>
                      <p className="text-reiva-gris-suave text-sm">
                        {locationData.hours.saturday}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </FadeUp>

          {/* Mapa de Google */}
          <FadeUp delay={0.4} className="h-full">
            <div className="relative h-full">
              <div className="bg-black/60 backdrop-blur-sm rounded-2xl px-6 h-full space-y-4">
                <h3 className="text-xl font-semibold text-reiva-gris-suave mb-4">
                  Nuestra Ubicación
                </h3>
                <div className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-reiva-primary-foreground/20 rounded-lg flex items-center justify-center">
                    <MapPin className="w-5 h-5 text-reiva-primary-foreground" />
                  </div>
                  <div>
                    <p className="text-reiva-gris-suave font-medium">
                      Dirección
                    </p>
                    <p className="text-reiva-gris-suave text-sm">
                      {locationData.address.street}, {locationData.address.city}
                    </p>
                    <p className="text-reiva-gris-suave/75 text-xs">
                      A media cuadra del Parque del Obelisco de los Héroes
                    </p>
                  </div>
                </div>
                <div className="relative overflow-hidden rounded-xl h-max">
                  <iframe
                    src="https://www.google.com/maps/embed?pb=!1m26!1m12!1m3!1d988.1856447206037!2d-78.0421173803972!3d-7.817049770870054!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!4m11!3e2!4m3!3m2!1d-7.8167547!2d-78.0410946!4m5!1s0x91ade50d6bb96cbd%3A0xd6f04d9efb30e157!2sESTUDIO%20CONTABLE%20REIVA%20S.A.C.%2C%20Jr.%20S%C3%A1nchez%20Carri%C3%B3n%201330%2C%20Huamachuco%2013301!3m2!1d-7.8173680999999995!2d-78.04185269999999!5e0!3m2!1ses-419!2spe!4v1754861052025!5m2!1ses-419!2spe"
                    width="100%"
                    height="400"
                    style={{
                      border: 0,
                      filter:
                        "invert(90%) hue-rotate(180deg) saturate(0.8) brightness(0.9)",
                    }}
                    allowFullScreen
                    loading="lazy"
                    referrerPolicy="no-referrer-when-downgrade"
                    className="w-full h-80 lg:h-[500px] rounded-lg"
                  ></iframe>
                  {/* Overlay para mejorar la integración visual */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent pointer-events-none rounded-lg"></div>
                </div>
                <p className="text-reiva-gris-suave/75 text-sm mt-3 text-center">
                  Haz clic en el mapa para ver direcciones detalladas
                </p>
              </div>
            </div>
          </FadeUp>
        </div>
      </div>
    </section>
  );
}

"use client";
import Image from "next/image";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import ReivaLogo from "@/assets/img/logo-reiva.svg";
import { ArrowLeftCircle } from "lucide-react";
import { signIn } from "next-auth/react";
import { useRouter } from "next/navigation";

export default function Login() {
  const { register, handleSubmit } = useForm();
  const router = useRouter();
  

  const onSubmit = handleSubmit(async (data) => {
    const signInResponse = await signIn("credentials", {
      username: data.username,
      password: data.password,
      redirect: false,
    });

    if (signInResponse?.error){
      console.error(signInResponse.error)
    }else {
      // const requestedPage = searchParams.get("p");
      // const nextRoute = requestedPage ? requestedPage : "/app";
      router.push("/app");
    }
  });

  return (
    <>
      <div className="fixed left-4 top-2">
        <Link
          href={"/"}
          className="flex items-center gap-1.5 [&>svg]:hover:rotate-45 text-reiva-primary"
        >
          <ArrowLeftCircle
            size={20}
            className="transition-all ease-in-out duration-300"
          />
          <span>Ir al sitio web</span>
        </Link>
      </div>
      <div className="w-full lg:grid  lg:grid-cols-2 px-1.5">
        <div className="flex items-center justify-center py-12">
          <form className="mx-auto grid w-[350px] gap-6" onSubmit={onSubmit}>
            <div className="grid gap-2 text-center space-y-3">
              <Image
                src={ReivaLogo}
                alt="Reiva Logo"
                className="mx-auto w-[80%] h-auto"
                width={0}
                height={0}
                loading="eager"
                priority
              />
              <span className="leading-none dark:text-gray-200 tracking-widest">
                Sistema de administración de noticias y novedades
              </span>
            </div>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="username">Usuario</Label>
                <Input
                  id="username"
                  type="text"
                  {...register("username", {
                    required: true,
                  })}
                />
              </div>
              <div className="grid gap-2">
                <div className="flex flex-wrap  items-center">
                  <Label htmlFor="password">Contraseña</Label>
                  <Link
                    href="/forgot-password"
                    className="ml-auto inline-block text-sm underline"
                  >
                    ¿Olvidó su contraseña?
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  {...register("password", {
                    required: true,
                  })}
                />
              </div>
              <Button
                type="submit"
                className="w-full dark:bg-reiva-primary-foreground dark:text-black dark:hover:bg-reiva-primary"
                variant={"reiva_yellow"}
              >
                Iniciar sesión
              </Button>
            </div>
            {/* <div className="mt-4 text-center text-sm">
              ¿No tiene una cuenta?{" "}
              <Link href="#" className="underline">
                Regístrese
              </Link>
            </div> */}
          </form>
        </div>
        <div className="hidden bg-muted lg:block">
          <Image
            src="/img/asesoria_tributaria.webp"
            alt="Image"
            width={500}
            height={500}
            className="h-screen w-full object-cover dark:brightness-[0.2] dark:grayscale"
          />
        </div>
      </div>
    </>
  );
}

import { ServiceContent, AboutUsContent, AnnouncementContent } from "@/types/cms";
import { servicesMock } from "@/mocks/services";
import { aboutUsMock } from "@/mocks/about";
import { announcementsMock } from "@/mocks/announcements";
import { locationMock, LocationContent } from "@/mocks/location";

export class CMSService {
  // Servicios
  static getAllServices(): ServiceContent[] {
    return servicesMock;
  }

  static getServiceBySlug(slug: string): ServiceContent | null {
    return servicesMock.find((service) => service.slug === slug) || null;
  }

  static getServicesByCategory(category: string): ServiceContent[] {
    return servicesMock.filter((service) => service.category === category);
  }

  // Sobre Nosotros
  static getAboutUsContent(): AboutUsContent {
    return aboutUsMock;
  }

  static getTeamMembers() {
    return aboutUsMock.teamMembers;
  }

  static getCompanyHistory() {
    return aboutUsMock.history;
  }

  static getCompanyValues() {
    return aboutUsMock.values;
  }

  static getCertifications() {
    return aboutUsMock.certifications || [];
  }

  // Anuncios
  static getAllAnnouncements(): AnnouncementContent[] {
    return announcementsMock.filter(announcement => announcement.isActive);
  }

  static getAnnouncementsByType(type: 'promotional' | 'deadline' | 'news'): AnnouncementContent[] {
    return announcementsMock.filter(announcement =>
      announcement.isActive && announcement.type === type
    );
  }

  static getFeaturedAnnouncements(limit: number = 4): AnnouncementContent[] {
    return announcementsMock
      .filter(announcement => announcement.isActive)
      .sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      })
      .slice(0, limit);
  }

  // Ubicación
  static getLocationContent(): LocationContent {
    return locationMock;
  }
}

"use client";

import { ServiceContent } from "@/types/cms";
import { <PERSON>R<PERSON>Icon, CheckCircle } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

export default function ServicesCard({ service }: { service: ServiceContent }) {
  return (
    <div className="relative min-h-[600px] group cursor-pointer transition-all duration-500 ease-in-out hover:scale-[1.02]">
      {/* Imagen de fondo con gradiente */}
      <div className="absolute inset-0 rounded-xl overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-b from-black/80 via-black/50 to-black/90 z-10 transition-opacity duration-500 group-hover:opacity-75" />
        <Image
          src={service.image || `/img/${service.slug}.webp`}
          alt={service.title}
          fill={true}
          className="object-cover object-center transition-transform duration-700 group-hover:scale-110"
          quality={90}
        />
      </div>

      {/* Contenido */}
      <div className="relative h-full z-20 p-8 flex flex-col justify-between">
        <div className="space-y-4">
          <Badge
            variant="secondary"
            className="mb-2 text-sm px-4 py-1 backdrop-blur-sm bg-white/10"
          >
            {service.category}
          </Badge>
          <h3 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            {service.title}
          </h3>
          <p className="text-gray-300 text-lg leading-relaxed max-w-xl">
            {service.description}
          </p>
        </div>

        {/* Características principales */}
        <div className="space-y-6 mt-8">
          <div className="space-y-3">
            <h4 className="text-xl font-semibold text-white/90">
              Características principales
            </h4>
            <ul className="grid grid-cols-1 gap-3">
              {service.features.slice(0, 3).map((feature, index) => (
                <li
                  key={index}
                  className="flex items-center gap-3 text-gray-300 group-hover:text-white transition-colors"
                >
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="text-base">{feature}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Precio y CTA */}
          <div className="flex items-end justify-between pt-6 border-t border-white/10">
            {service.pricing && (
              <div className="space-y-1">
                <p className="text-gray-400">Desde</p>
                <div className="text-3xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                  S/. {service.pricing.startingFrom}/mes
                </div>
              </div>
            )}
            <Link href={`/servicios/${service.slug}`} className="group/link">
              <Button
                variant="ghost"
                className="gap-2 text-white hover:text-white hover:bg-white/10"
              >
                Ver más
                <ArrowRightIcon className="w-4 h-4 transition-transform duration-300 group-hover/link:translate-x-1" />
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

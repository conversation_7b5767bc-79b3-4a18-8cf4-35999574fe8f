"use client";

import { useEffect, useState } from "react";
import { CMSService } from "@/services/cms";
import { ServiceContent } from "@/types/cms";
import { FadeUp } from "@/components/animations/fade";
import { cn } from "@/lib/utils";

export default function Services() {
  const services = CMSService.getAllServices();

  return (
    <section className="py-24 bg-gradient-to-b from-background to-background/80">
      <div className="space-y-16">
        <div className="text-center space-y-6 max-w-4xl mx-auto">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
            Nuestros Servicios
          </h2>
          <p className="text-lg md:text-xl text-gray-300 leading-relaxed">
            Nuestra asistencia no se limita a resolver problemas, sino que
            también se enfoca en prevenir y planificar de manera adecuada los
            negocios, para evitar que nuestros clientes enfrenten problemas en
            el futuro.
          </p>
        </div>
        <div className="space-y-0">
          {services.map((service, index) => (
            <FadeUp
              key={service.id}
              whileInView={{
                y: 0,
                x: index % 2 === 0 ? -100 : 100,
              }}
            >
              <div
                key={service.id}
                className="flex flex-col lg:flex-row items-center gap-8 min-h-[600px] w-full"
              >
                <div
                  className={`w-full lg:w-1/2 p-8 lg:p-12 space-y-6 order-2 ${
                    index % 2 === 0 ? "lg:order-1" : "lg:order-2"
                  }`}
                >
                  <span className="inline-block px-4 py-2 rounded-full bg-reiva-primary/10 text-reiva-primary-foreground text-sm font-medium">
                    {service.category}
                  </span>
                  <h3 className="text-3xl lg:text-4xl font-bold">
                    {service.title}
                  </h3>
                  <p className="text-lg text-gray-300">{service.description}</p>
                  <ul className="space-y-3">
                    {service.features.slice(0, 5).map((feature, idx) => (
                      <li
                        key={idx}
                        className="flex items-center gap-2 text-gray-300"
                      >
                        <span className="text-reiva-primary-foreground">✓</span>
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <div className="flex items-center gap-4">
                    <a
                      href={`/servicios/${service.slug}`}
                      className="inline-flex items-a vecenter gap-2 px-6 py-3 rounded-full bg-reiva-primary hover:bg-reiva-primary-foreground transition-colors duration-300 text-black font-medium"
                    >
                      Ver más
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </a>
                  </div>
                </div>
                <div
                  className={`relative w-full h-[300px] lg:h-screen max-h-[900px] lg:w-1/2 aspect-[4/3] overflow-hidden order-1 ${
                    index % 2 === 0 ? "lg:order-2" : "lg:order-1"
                  }`}
                >
                  <div
                    className="absolute inset-0 bg-cover bg-center transform transition-transform duration-700 hover:scale-105"
                    style={{ backgroundImage: `url(${service.image})` }}
                  >
                    <div
                      className={cn(
                        "absolute inset-0",
                        index % 2 === 0 &&
                          "bg-gradient-to-r from-black via-black/10 to-transparent",
                        index % 2 !== 0 &&
                          "bg-gradient-to-r from-transparent via-black/10 to-black"
                      )}
                    />
                  </div>
                </div>
              </div>
            </FadeUp>
          ))}
        </div>
      </div>
    </section>
  );
}

import { config } from "@/lib/config";
import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  const baseUrl = config.WEBSITE_URL || "";

  return {
    rules: [
      {
        userAgent: "*",
        allow: "/",
        disallow: [
          "/app/",
          "/api/",
          "/login",
          "/maintenance",
          "/_next/",
          "/admin/",
          "/*?*", // Evitar parámetros de consulta innecesarios
        ],
      },
      // Reglas específicas para Googlebot
      {
        userAgent: "Googlebot",
        allow: "/",
        disallow: [
          "/app/",
          "/api/",
          "/login",
          "/maintenance",
          "/_next/",
          "/admin/",
        ],
      },
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl,
  };
}

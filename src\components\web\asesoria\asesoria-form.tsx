"use client";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { AsesoriaFormSchema } from "@/components/web/asesoria/schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import ComboboxServicios from "./partials/combobox-servicios";
import { AnimatePresence, motion } from "motion/react";
import { Textarea } from "@/components/ui/textarea";

export default function FormAsesoria() {
  const form = useForm<z.infer<typeof AsesoriaFormSchema>>({
    resolver: zodResolver(AsesoriaFormSchema),
    defaultValues: {
      name: "",
      business_size: undefined,
      message: "",
      service_type: "",
      other_service: "",
    },
  });

  const watch_service_type = form.watch("service_type");

  async function onSubmit(data: z.infer<typeof AsesoriaFormSchema>) {
    console.log(data);

    // Construir el mensaje de WhatsApp
    let message = `¡Hola! Soy *${data.name}* y me gustaría solicitar asesoría contable.\n\n`;

    message += `*Busco asesoría sobre*: ${
      data.service_type === "Otro" ? data.other_service : data.service_type
    }\n`;

    if (data.business_size && data.business_size !== "planeacion") {
      message += `*Tamaño de empresa*: ${data.business_size}\n`;
    } else if (data.business_size === "planeacion") {
      message += "*Quiero constituir una empresa*\n";
    }

    if (data.message) {
      message += `\n*Detalles adicionales*:\n${data.message}`;
    }

    // Codificar correctamente el mensaje para la URL
    const encodedMessage = encodeURIComponent(message);
    const whatsappURL = `https://wa.me/982673186?text=${encodedMessage}`;

    window.open(whatsappURL, "_blank");
  }

  const getBusinessSizeLabelColor = (
    value: string,
    fieldValue: string | undefined
  ) => {
    if (value === fieldValue) return "text-reiva-primary-foreground";
    else return "";
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="w-full flex flex-col gap-4 min-h-[450px]"
      >
        <div className="dark:[&>div>input]:placeholder:text-muted space-y-4 [&>div>label]:font-bold text-primary">
          {/* Nombre completo */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>¿Cuál es tu nombre completo? *</FormLabel>
                <FormControl>
                  <Input placeholder="Ej: Juan Pérez García" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Tamaño de empresa (solo para personas jurídicas) */}
          <FormField
            control={form.control}
            name="business_size"
            render={({ field }) => (
              <FormItem>
                <FormLabel>¿Cuál es el tamaño de tu empresa?</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="grid grid-cols-2 gap-2 dark:[&>div>button]:border-reiva-primary dark:[&>div>button>span]:bg-reiva-primary dark:[&>div>button>span]:text-black"
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="micro" />
                      </FormControl>
                      <FormLabel
                        className={getBusinessSizeLabelColor(
                          "micro",
                          field.value
                        )}
                      >
                        Microempresa
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="pequeña" />
                      </FormControl>
                      <FormLabel
                        className={getBusinessSizeLabelColor(
                          "pequeña",
                          field.value
                        )}
                      >
                        Pequeña
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="mediana" />
                      </FormControl>
                      <FormLabel
                        className={getBusinessSizeLabelColor(
                          "mediana",
                          field.value
                        )}
                      >
                        Mediana
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="grande" />
                      </FormControl>
                      <FormLabel
                        className={getBusinessSizeLabelColor(
                          "grande",
                          field.value
                        )}
                      >
                        Grande
                      </FormLabel>
                    </FormItem>
                    {/* Opción para quienes estén planeando o aún constituyendo una empresa  */}
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="planeacion" />
                      </FormControl>
                      <FormLabel
                        className={getBusinessSizeLabelColor(
                          "planeacion",
                          field.value
                        )}
                      >
                        Quiero constituir una empresa
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <ComboboxServicios form={form} />

          {/* Otro */}
          <AnimatePresence>
            {watch_service_type === "Otro" && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                <FormField
                  control={form.control}
                  name="other_service"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          placeholder="Especifique el servicio que necesita consultar"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>
            )}
          </AnimatePresence>

          <FormField
            control={form.control}
            name="message"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  ¿Hay algo específico que te gustaría consultar? (Opcional)
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Ej: Necesito ayuda con la declaración anual de mi empresa, tengo dudas sobre los libros electrónicos..."
                    className="resize-none"
                    rows={3}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="space-y-4">
          <Button
            type="submit"
            variant={"reiva_yellow"}
            className="w-full text-black font-semibold"
          >
            Solicitar asesoría gratuita por WhatsApp
          </Button>
          <p className="text-xs text-center text-muted-foreground">
            Al enviar este formulario, serás redirigido a WhatsApp para
            continuar la conversación con nuestro equipo de expertos.
          </p>
        </div>
      </form>
    </Form>
  );
}

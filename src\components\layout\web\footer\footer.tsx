"use client";
import {
  UBICACION_Y_CONTACTO,
  REDES_SOCIALES,
  SERVICIOS_CONTABLES,
} from "@/constants/web/footer-data";
import Logo from "@/assets/img/logo-reiva.svg";
import Image from "next/image";
import ListaFooter from "./partials/lista-footer";

function WebFooter() {
  return (
    <footer className="bg-transparent border-t">
      <div className="mx-auto w-full max-w-screen-xl p-4 py-6 lg:py-8">
        <div className="lg:flex md:justify-between gap-x-12 space-y-4">
          <div className="mb-6 md:mb-0">
            <Image
              src={Logo}
              className="w-auto h-20"
              width={40}
              height={40}
              alt="Logo Reiva"
            />
          </div>
          <div className="flex flex-wrap gap-8">
            <ListaFooter footerData={SERVICIOS_CONTABLES} />
            <ListaFooter footerData={UBICACION_Y_CONTACTO} />
            <ListaFooter footerData={REDES_SOCIALES} />
          </div>
        </div>
        <hr className="my-6 border-gray-200 sm:mx-auto dark:border-gray-700 lg:my-8" />
        <div className="flex flex-col items-center justify-between">
          <span className="text-sm text-gray-500 sm:text-center dark:text-gray-400">
            {new Date().getFullYear()} ESTUDIO CONTABLE REIVA S.A.C.
          </span>
          <span className="text-sm text-reiva-primary-foreground">
            RUC 20612408166
          </span>
        </div>
      </div>
    </footer>
  );
}

export default WebFooter;

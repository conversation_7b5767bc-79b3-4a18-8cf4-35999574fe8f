"use client";
import NavInfo from "@/components/layout/web/navigation/info";
import NavBar from "@/components/layout/web/navigation/navigation-desktop";
import {
  useMotionValueEvent,
  useScroll,
  motion,
  useTransform,
} from "motion/react";
import { useState } from "react";

export default function Header() {
  const [lastScrollPos, setLastScrollPos] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const { scrollYProgress } = useScroll();

  useMotionValueEvent(scrollYProgress, "change", (latest) => {
    if (latest > lastScrollPos) {
      setIsVisible(false);
    } else {
      setIsVisible(true);
    }
    setLastScrollPos(latest);
  });

  const infoBackground = useTransform(
    scrollYProgress,
    [0, 1],
    ["transparent", "rgba(0, 0, 0, 0.5)"]
  );

  return (
    <motion.header
      className="z-50 sticky top-0 bg-transparent"
      initial={{ y: 0 }}
      animate={{ y: isVisible ? -1 : -88 }}
      transition={{ ease: "easeOut", duration: 0.3, delay: 0.4 }}
    >
      <div className="bg-primary-foreground">
        <div className="px-2 sm:container">
          <NavInfo />
        </div>
      </div>
      <motion.div
        className="
          bg-transparent
        "
        style={{
          background: infoBackground,
          backdropFilter: scrollYProgress.get() > 0 ? "blur(100px)" : "none",
        }}
      >
        <div className="px-0 sm:container">
          <NavBar />
        </div>
      </motion.div>
    </motion.header>
  );
}

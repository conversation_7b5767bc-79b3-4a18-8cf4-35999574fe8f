import { servicesMock } from "@/mocks/services";
import { ServiceCategory } from "@/types/cms";

// Función para generar datos de navegación desde los mocks de servicios
function generateServiciosFromMocks() {
  const serviciosByCategory = servicesMock.reduce((acc, service) => {
    if (!acc[service.category]) {
      acc[service.category] = [];
    }
    acc[service.category].push(service);
    return acc;
  }, {} as Record<ServiceCategory, typeof servicesMock>);

  return [
    {
      id: 1,
      title: "Asesoría Contable",
      href: "/servicios/asesoria-contable",
      description: serviciosByCategory[ServiceCategory.ASESORIA_CONTABLE]?.[0]?.features.join('\n') || '',
      services: serviciosByCategory[ServiceCategory.ASESORIA_CONTABLE] || []
    },
    {
      id: 2,
      title: "Asesoría Tributaria",
      href: "/servicios/asesoria-tributaria",
      description: serviciosByCategory[ServiceCategory.ASESORIA_TRIBUTARIA]?.[0]?.features.join('\n') || '',
      services: serviciosByCategory[ServiceCategory.ASESORIA_TRIBUTARIA] || []
    },
    {
      id: 3,
      title: "Contrataciones Públicas",
      href: "/servicios/contrataciones-publicas",
      description: serviciosByCategory[ServiceCategory.CONTRATACIONES_PUBLICAS]?.[0]?.features.join('\n') || '',
      services: serviciosByCategory[ServiceCategory.CONTRATACIONES_PUBLICAS] || []
    },
  ];
}

export const SERVICIOS: {
  id: number;
  title: string;
  href: string;
  description: string;
  services: typeof servicesMock;
}[] = generateServiciosFromMocks();


  export const APPS: { title: string; href: string; description: string }[] = [
    {
        title: "Consulta Integrada de Comprobantes",
        // href: "/apps/consulta-integrada-cdp",
        href: "#",
        description: "Consulta de comprobantes de pago para personas jurídicas y naturales",
    },
    { 
        title: "Consulta RUC",
        // href: "/apps/consulta-de-ruc",
        href: "#",
        description: "Consulta información general y el estado de tu empresa",
    }, 
    ]
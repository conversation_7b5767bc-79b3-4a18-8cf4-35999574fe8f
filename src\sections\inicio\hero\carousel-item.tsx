import { Button, buttonVariants } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

interface CarouselSlideProps {
  image: string;
  title: string;
  description: JSX.Element;
}

function CarouselSlide({ image, title, description }: CarouselSlideProps) {
  return (
    <div className="relative h-full flex items-end md:items-center justify-center md:justify-start">
      <Image
        src={image}
        className="brightness-[40%] w-full h-full object-cover object-center opacity-75"
        width={1920}
        height={500}
        alt={title}
        priority={true}
      />
      <div className="absolute bg-black/50 backdrop-blur-sm md:rounded-lg p-6 md:p-8 mx-4 md:mx-0 md:left-[40px] text-center md:text-start space-y-4 w-full md:max-w-2xl">
        <div>
          <h1 className="font-bold text-2xl sm:text-3xl md:text-4xl lg:text-5xl uppercase text-reiva-primary-foreground leading-tight">
            {title}
          </h1>
          <div className="mt-4 sm:max-w-lg md:max-w-md lg:max-w-lg text-sm sm:text-base [&>p>strong]:text-reiva-primary [&>p]:leading-relaxed">
            {description}
          </div>
        </div>
        <Link
          href="/#contacto"
          className={buttonVariants({
            variant: "reiva_yellow",
            size: "lg",
          })}
        >
          Contáctanos
        </Link>
      </div>
    </div>
  );
}

export default CarouselSlide;

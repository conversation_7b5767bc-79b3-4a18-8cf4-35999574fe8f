import { FadeUp } from "@/components/animations/fade";
import AboutUsSection from "../partials/about-us-section";
import { CMSService } from "@/services/cms";
import { Calendar } from "lucide-react";

export default function History() {
  const aboutData = CMSService.getAboutUsContent();

  return (
    <section className="py-20 bg-black/80 backdrop-blur-sm">
      <div className="container">
        <FadeUp>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4 text-reiva-gris-suave">
              Nuestra{" "}
              <span className="text-reiva-primary-foreground">Historia</span>
            </h2>
            <p className="text-reiva-gris-suave text-lg max-w-2xl mx-auto">
              Un recorrido de crecimiento, innovación y compromiso con la
              excelencia profesional
            </p>
          </div>
        </FadeUp>
        <div className="relative">
          {/* <PERSON><PERSON>ea de tiempo */}
          <div className="absolute left-8 md:left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-reiva-primary to-reiva-primary-foreground transform md:-translate-x-1/2"></div>

          <div className="space-y-12">
            {aboutData.history.map((milestone, index) => (
              <FadeUp key={index} delay={index * 0.1}>
                <div
                  className={`relative flex items-center ${
                    index % 2 === 0 ? "md:flex-row" : "md:flex-row-reverse"
                  }`}
                >
                  {/* Punto en la línea de tiempo */}
                  <div className="absolute left-8 md:left-1/2 w-4 h-4 bg-reiva-primary rounded-full transform md:-translate-x-1/2 z-10 shadow-lg">
                    <div className="absolute inset-0 bg-reiva-primary rounded-full animate-ping opacity-75"></div>
                  </div>

                  {/* Contenido */}
                  <div
                    className={`w-full md:w-5/12 ml-16 md:ml-0 ${
                      index % 2 === 0 ? "md:pr-8" : "md:pl-8"
                    }`}
                  >
                    <div className="group p-8 bg-black/40 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-reiva-primary-foreground/20 hover:border-reiva-primary-foreground/50">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-reiva-primary-foreground to-reiva-primary flex items-center justify-center shadow-lg">
                          <Calendar className="w-6 h-6 text-black" />
                        </div>
                        <h3 className="text-2xl font-bold text-reiva-primary-foreground">
                          {milestone.year}
                        </h3>
                      </div>
                      <p className="text-reiva-gris-suave leading-relaxed">
                        {milestone.milestone}
                      </p>
                    </div>
                  </div>
                </div>
              </FadeUp>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}

import { FooterData } from "@/types/employee/footer-data-type";
import {
  MapPin,
  MapPinnedIcon,
  Mail,
  Phone,
  LucideBadgeCheck,
  Facebook,
} from "lucide-react";
import { InstagramLogoIcon } from "@radix-ui/react-icons";

export const SERVICIOS_CONTABLES: FooterData = {
  title: "Servicios Contables",
  items: [
    { icon: LucideBadgeCheck, description: "Declaración Mensual de Impuestos" },
    { icon: LucideBadgeCheck, description: "Declaración Anual de Impuestos" },
    {
      icon: LucideBadgeCheck,
      description: "Trámites ante SUNAT, SUNARP, OSCE",
    },
    {
      icon: LucideBadgeCheck,
      description: "Constitución de Empresas y más ...",
    },
  ],
};

export const REDES_SOCIALES: FooterData = {
  title: "Síguenos",
  items: [
    {
      icon: Facebook,
      description: "Facebook",
      link: "https://www.facebook.com/profile.php?id=61558604646273",
    },
    {
      icon: InstagramLogoIcon,
      description: "Instagram",
      link: "https://www.instagram.com/estudiocontable.reiva",
    },
  ],
};

export const UBICACION_Y_CONTACTO: FooterData = {
  title: "Ubicación y Contacto",
  items: [
    { icon: MapPin, description: "Huamachuco, Perú" },
    { icon: MapPinnedIcon, description: "Jr. Sánchez Carrión 1330" },
    { icon: Mail, description: "<EMAIL>" },
    { icon: Phone, description: "+51 982 673 186" },
  ],
};

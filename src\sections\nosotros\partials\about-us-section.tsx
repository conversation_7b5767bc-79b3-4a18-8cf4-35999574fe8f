import { Fade } from "@/components/animations/fade";
import { cn } from "@/lib/utils";
import Image from "next/image";
import React from "react";

interface Props extends React.HTMLAttributes<HTMLElement> {
  image: string;
  title: string;
  children: React.ReactNode;
  reverse?: boolean;
}

export default function AboutUsSection({
  image,
  title,
  reverse,
  children,
  ...props
}: Props) {
  return (
    <article
      className={cn(
        "container relative flex flex-col md:flex-row",
        reverse && "md:flex-row-reverse"
      )}
    >
      <aside className="grow w-full md:w-[0.6] relative min-h-[300px]">
        {/* <Fade delay={0.1} direction={reverse ? "left" : "right"}> */}
        <Image
          src={image}
          alt={title}
          className="object-cover object-top h-full w-full z-0"
          sizes="(max-width: 1440px) 100vw, 33vw"
          loading="lazy"
          fill
        />
        {/* </Fade> */}
      </aside>

      {/* Main content */}
      <Fade
        className={cn(
          "w-full bg-primary-foreground/90 z-[2] p-8 my-4",
          reverse ? "md:-mr-[10vw]" : "md:-ml-[10vw]",
          props.className
        )}
        direction={reverse ? "right" : "left"}
        delay={0.5}
      >
        <section>
          <h2 className="text-4xl tracking-tight font-bold">{title}</h2>
          {children}
        </section>
      </Fade>
    </article>
  );
}

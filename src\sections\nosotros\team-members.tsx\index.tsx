import { FadeUp } from "@/components/animations/fade";
import { CMSService } from "@/services/cms";
import { Users } from "lucide-react";
import Image from "next/image";

export default function index() {
  const aboutData = CMSService.getAboutUsContent();

  return (
    <section className="py-20">
      <div className="container">
        <FadeUp>
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4 text-reiva-gris-suave">
              Nuestro{" "}
              <span className="text-reiva-primary-foreground">Equipo</span>
            </h2>
            <p className="text-reiva-gris-suave text-lg max-w-2xl mx-auto">
              Profesionales especializados comprometidos con la excelencia y el
              éxito de nuestros clientes
            </p>
          </div>
        </FadeUp>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {aboutData.teamMembers.map((member, index) => (
            <FadeUp key={member.id} delay={index * 0.1}>
              <div className="group relative overflow-hidden rounded-2xl bg-black/40 backdrop-blur-sm shadow-lg hover:shadow-2xl transition-all duration-300 border border-reiva-primary-foreground/20">
                <div className="aspect-[4/5] relative overflow-hidden">
                  <Image
                    src={member.image || ""}
                    alt={member.name}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-6 text-reiva-gris-suave">
                  <div className="space-y-2">
                    <h3 className="text-xl font-bold text-reiva-gris-suave">
                      {member.name}
                    </h3>
                    <p className="text-reiva-primary-foreground font-medium">
                      {member.position}
                    </p>
                    <p className="text-sm text-reiva-gris-suave line-clamp-3">
                      {member.bio}
                    </p>
                  </div>
                </div>
                <div className="absolute top-4 right-4 w-12 h-12 bg-reiva-primary-foreground/90 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <Users className="w-6 h-6 text-black" />
                </div>
              </div>
            </FadeUp>
          ))}
        </div>
      </div>
    </section>
  );
}

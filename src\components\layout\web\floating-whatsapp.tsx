"use client"

import WhatsAppIcon from "@/assets/icons/whatsapp-2.svg";
import Image from "next/image";
import {
  TooltipContent,
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import Link from "next/link";

export default function WhatsAppFloating() {
    return (
        <TooltipProvider delayDuration={100}>
        <Tooltip defaultOpen={true}>
          <TooltipTrigger className="fixed bottom-4 right-4 rounded-full z-50">
            <Link href="https://wa.me/982673186">
                <Image src={WhatsAppIcon} alt="WhatsApp" width={40} height={40} />
                <span className="sr-only">Solicitar asesoría</span>
            </Link>
          </TooltipTrigger>
          <TooltipContent
            side="left"
            sideOffset={5}
          >
              <p className="">Solicita asesoría por WhatsApp</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
}
import { Mail, PhoneCallIcon } from "lucide-react";

export default function ContactDetails() {
  return (
    <ul
      role="list"
      className="flex flex-wrap items-center gap-2 space-y-4 sm:space-y-0 [&>li]:flex [&>li]:items-center [&>li]:gap-4"
    >
      <li>
        <a
          aria-label="Enviar correo electrónico"
          className="hover:text-reiva-primary"
          href="mailto:<EMAIL>"
        >
          <Mail size={20} className="text-reiva-primary" />
          {/* <EMAIL> */}
        </a>
      </li>
      <li>
        <a
          aria-label="Mensaje vía WhatsApp"
          className="hover:text-reiva-primary"
          href="https://wa.me/982673186?text=Hola%2C%20quisiera%20solicitar%20una%20asesoría%20contable"
        >
          <PhoneCallIcon size={20} className="text-reiva-primary" />

          {/* +51 982 673 186 */}
        </a>
      </li>
    </ul>
  );
}

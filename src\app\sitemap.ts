import { MetadataRoute } from "next";
import { CMSService } from "@/services/cms";
import { config } from "@/lib/config";

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = config.WEBSITE_URL || "";
  const currentDate = new Date();

  // Obtener todos los servicios para generar rutas dinámicas
  const services = CMSService.getAllServices();

  // Rutas estáticas principales
  const staticRoutes: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: currentDate,
      changeFrequency: "weekly",
      priority: 1.0,
    },
    {
      url: `${baseUrl}/nosotros`,
      lastModified: currentDate,
      changeFrequency: "monthly",
      priority: 0.8,
    },
  ];

  // Rutas dinámicas de servicios individuales (alta prioridad)
  const serviceRoutes: MetadataRoute.Sitemap = services.map((service) => ({
    url: `${baseUrl}/servicios/${service.slug}`,
    lastModified: new Date(service.updatedAt),
    changeFrequency: "monthly" as const,
    priority: 0.9, // Alta prioridad para páginas de servicios específicos
  }));

  // Combinar todas las rutas y eliminar duplicados
  const allRoutes = [...staticRoutes, ...serviceRoutes];

  // Eliminar duplicados basados en URL (en caso de que haya URLs repetidas)
  const uniqueRoutes = allRoutes.filter(
    (route, index, self) => index === self.findIndex((r) => r.url === route.url)
  );

  return uniqueRoutes;
}

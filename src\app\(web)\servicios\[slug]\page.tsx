import { CMSService } from "@/services/cms";
import { Metadata } from "next";
import { notFound } from "next/navigation";
import { <PERSON><PERSON>Check, LucideHelpCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import FadeUp from "@/components/animations/fade-up";
import { MotionHighlight } from "@/components/animate-ui/effects/motion-highlight";
import {
  Accordion,
  AccordionItem,
} from "@/components/animate-ui/headless/accordion";
import ServicesFAQs from "@/sections/services/faqs";

type Props = {
  params: {
    slug: string;
  };
};

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const service = CMSService.getServiceBySlug(params.slug);

  if (!service) {
    return {
      title: "Servicio no encontrado",
    };
  }

  return {
    title: `${service.title} | Reiva Estudio Contable`,
    description: service.description,
  };
}

export default function ServicePage({ params }: Props) {
  const service = CMSService.getServiceBySlug(params.slug);
  const allServices = CMSService.getAllServices();

  if (!service) {
    notFound();
  }

  const otherServices = allServices
    .filter((s) => s.id !== service.id)
    .slice(0, 3);

  return (
    <div className="min-h-screen bg-background">
      <section className="relative h-[600px] overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center"
          style={{ backgroundImage: `url(${service.image})` }}
        >
          <div className="absolute inset-0 bg-gradient-to-b from-black/90 via-black/80 to-background" />
        </div>
        <div className="container mx-auto h-full relative">
          <div className="flex flex-col justify-center h-full text-white space-y-6 max-w-3xl px-4">
            <Badge
              variant="outline"
              className="w-fit text-reiva-primary-foreground"
            >
              {service.category}
            </Badge>
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold">
              {service.title}
            </h1>
            <p className="text-xl text-white/80">{service.description}</p>
            {service.pricing && (
              <div className="flex items-center gap-4 pt-4">
                <p className="text-3xl font-bold text-reiva-primary-foreground">
                  S/. {service.pricing.startingFrom}
                  <span className="text-base font-normal text-gray-300">
                    /mes
                  </span>
                </p>
              </div>
            )}
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-24">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-16">
            {/* Stats Section */}
            {/* <section className="py-12">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                <FadeUp delay={0.1}>
                  <div className="text-center space-y-2">
                    <p className="text-4xl font-bold text-reiva-primary-foreground">+500</p>
                    <p className="text-sm text-gray-300">Clientes Satisfechos</p>
                  </div>
                </FadeUp>
                <FadeUp delay={0.2}>
                  <div className="text-center space-y-2">
                    <p className="text-4xl font-bold text-reiva-primary-foreground">
                      15+
                    </p>
                    <p className="text-sm text-gray-300">Años de Experiencia</p>
                  </div>
                </FadeUp>
                <FadeUp delay={0.3}>
                  <div className="text-center space-y-2">
                    <p className="text-4xl font-bold text-reiva-primary-foreground">
                      100%
                    </p>
                    <p className="text-sm text-gray-300">Cumplimiento Legal</p>
                  </div>
                </FadeUp>
                <FadeUp delay={0.4}>
                  <div className="text-center space-y-2">
                    <p className="text-4xl font-bold text-reiva-primary-foreground">
                      24/7
                    </p>
                    <p className="text-sm text-gray-300">Soporte Disponible</p>
                  </div>
                </FadeUp>
              </div>
            </section> */}

            {/* Features Section */}
            <section>
              <div className="space-y-8">
                <div className="space-y-4">
                  <h2 className="text-3xl font-bold text-white">
                    ¿Qué incluye?
                  </h2>
                  <p className="text-lg text-gray-300">
                    Descubre todo lo que incluye nuestro servicio de{" "}
                    {service.title.toLowerCase()}
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {service.features.map((feature, index) => (
                    <FadeUp key={index} delay={index * 0.1}>
                      <div className="p-6 rounded-xl bg-card/5 border border-white/5 backdrop-blur-sm hover:bg-card/10 transition-colors flex items-start gap-4 h-full">
                        <LucideCheck className="h-6 w-6 text-reiva-primary-foreground shrink-0" />
                        <p className="text-lg text-white">{feature}</p>
                      </div>
                    </FadeUp>
                  ))}
                </div>
              </div>
            </section>

            {/* Benefits Section */}
            <section>
              <div className="space-y-8">
                <div className="space-y-4">
                  <h2 className="text-3xl font-bold text-white">Beneficios</h2>
                  <p className="text-lg text-gray-300">
                    ¿Por qué elegir nuestro servicio de{" "}
                    <strong className="text-reiva-primary-foreground">
                      {service.title.toLowerCase()}
                    </strong>
                    ?
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <MotionHighlight
                    hover
                    className="rounded-xl dark:bg-reiva-primary-foreground/30"
                  >
                    {service.benefits.map((benefit, index) => (
                      <FadeUp key={index} delay={index * 0.1}>
                        <div className="p-8 rounded-xl bg-card/5 border border-white/5 backdrop-blur-sm hover:bg-card/10 transition-colors flex flex-col min-h-[350px]">
                          <h3 className="text-2xl font-semibold text-white mb-4">
                            {benefit.title}
                          </h3>
                          <p className="text-gray-300 flex-grow">
                            {benefit.description}
                          </p>
                        </div>
                      </FadeUp>
                    ))}
                  </MotionHighlight>
                </div>
              </div>
            </section>

            {/* Value Proposition */}
            <section className="py-12">
              <div className="rounded-2xl bg-reiva-primary/10 border border-reiva-primary/20 p-8 space-y-6">
                <h2 className="text-3xl font-bold text-white">
                  ¿Por qué elegirnos?
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-reiva-primary-foreground">
                      Experiencia Comprobada
                    </h3>
                    <p className="text-gray-300">
                      Nuestro equipo de expertos cuenta con años de experiencia
                      en el sector, brindando soluciones efectivas y
                      personalizadas para cada cliente.
                    </p>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-reiva-primary-foreground">
                      Tecnología Avanzada
                    </h3>
                    <p className="text-gray-300">
                      Utilizamos las últimas herramientas y software del mercado
                      para garantizar un servicio eficiente y actualizado.
                    </p>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-reiva-primary-foreground">
                      Atención Personalizada
                    </h3>
                    <p className="text-gray-300">
                      Cada cliente recibe un trato único y personalizado,
                      adaptándonos a sus necesidades específicas y objetivos
                      empresariales.
                    </p>
                  </div>
                  <div className="space-y-4">
                    <h3 className="text-xl font-semibold text-reiva-primary-foreground">
                      Resultados Garantizados
                    </h3>
                    <p className="text-gray-300">
                      Nos comprometemos con el éxito de tu empresa, garantizando
                      resultados medibles y un retorno de inversión positivo.
                    </p>
                  </div>
                </div>
              </div>
            </section>
          </div>

          {/* Aside */}
          <aside className="lg:col-span-1 space-y-8">
            <div className="sticky top-24">
              <div className="space-y-8">
                <div className="rounded-xl bg-card/5 border border-white/5 backdrop-blur-sm p-6 space-y-6">
                  <h3 className="text-xl font-semibold text-white">
                    Otros servicios
                  </h3>
                  <div className="space-y-4">
                    {otherServices.map((otherService) => (
                      <a
                        key={otherService.id}
                        href={`/servicios/${otherService.slug}`}
                        className="block p-4 rounded-lg bg-background/50 hover:bg-background transition-colors group"
                      >
                        <span className="text-sm text-reiva-primary-foreground">
                          {otherService.category}
                        </span>
                        <h4 className="text-lg font-medium text-white group-hover:text-reiva-primary-foreground transition-colors">
                          {otherService.title}
                        </h4>
                        <p className="text-sm text-gray-400 line-clamp-2">
                          {otherService.description}
                        </p>
                      </a>
                    ))}
                  </div>
                </div>

                <div className="rounded-xl bg-reiva-primary/10 border border-reiva-primary/20 p-8 space-y-6 text-center">
                  <h3 className="text-2xl font-bold text-white">
                    Agendemos una cita
                  </h3>
                  <p className="text-gray-300">
                    Descubre cómo podemos ayudarte a alcanzar tus objetivos
                    empresariales. Nuestro equipo está listo para brindarte una
                    asesoría personalizada y resolver todas tus dudas.
                  </p>
                  <a
                    href="https://wa.me/51999999999"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 px-6 py-3 bg-reiva-primary text-white rounded-lg hover:bg-reiva-primary/90 transition-colors"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="w-5 h-5"
                    >
                      <path d="M3 21l1.9-5.7a8.5 8.5 0 113.4 3.4z"></path>
                    </svg>
                    Contactar
                  </a>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </div>
      {/* FAQ Section */}
      {service.faqs && service.faqs.length > 0 && (
        <section className="py-24 bg-background">
          <div className="container mx-auto px-4">
            <div className="space-y-12">
              <div className="text-center space-y-4">
                <h2 className="text-4xl font-bold text-white">
                  Preguntas Frecuentes
                </h2>
                <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                  Resolvemos tus dudas sobre nuestro servicio de{" "}
                  {service.title.toLowerCase()}
                </p>
              </div>
              <ServicesFAQs faqs={service.faqs} />
            </div>
          </div>
        </section>
      )}
    </div>
  );
}

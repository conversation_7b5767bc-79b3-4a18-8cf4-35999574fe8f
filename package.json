{"name": "reiva", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.7", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@vercel/analytics": "^1.4.1", "@vercel/speed-insights": "^1.1.0", "bcrypt": "^5.1.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^0.2.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "embla-carousel-reactive-utils": "^8.6.0", "lucide-react": "^0.368.0", "motion": "^11.18.2", "next": "^14.2.21", "next-auth": "^4.24.7", "next-themes": "^0.3.0", "radix-ui": "^1.4.2", "react": "^18", "react-dom": "^18", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.51.3", "sonner": "^1.4.41", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "three": "^0.179.1", "zod": "^3.23.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-google-recaptcha": "^2.1.9", "@types/three": "^0.179.0", "eslint": "^8", "eslint-config-next": "14.2.1", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}